# BookSystem 登录注册系统使用指南

## 概述

本项目实现了一个基于 TDesign Web 组件库的现代化用户登录注册界面，支持密码登录和验证码登录两种方式。

## 技术栈

- **前端框架**: Vue 3 + TypeScript
- **UI组件库**: TDesign Vue Next
- **状态管理**: Pinia
- **路由管理**: Vue Router
- **HTTP客户端**: Axios
- **构建工具**: Vite

## 功能特性

### 🔐 认证功能
- ✅ 密码登录
- ✅ 验证码登录
- ✅ 用户注册
- ✅ 自动登录状态管理
- ✅ 路由守卫保护

### 📱 用户体验
- ✅ 响应式设计
- ✅ 现代化UI设计
- ✅ 流畅的动画过渡
- ✅ 表单验证和错误提示
- ✅ 验证码倒计时功能

### 🛡️ 安全特性
- ✅ 邮箱格式验证
- ✅ 密码强度检测
- ✅ 确认密码验证
- ✅ Token自动管理
- ✅ 请求拦截器

## 项目结构

```
src/
├── api/
│   └── auth.ts              # 认证API接口
├── components/
│   ├── LoginForm.vue        # 登录表单组件
│   └── RegisterForm.vue     # 注册表单组件
├── stores/
│   └── auth.ts              # 认证状态管理
├── views/
│   ├── LoginView.vue        # 登录注册页面
│   ├── HomeView.vue         # 首页
│   └── AboutView.vue        # 关于页面
├── router/
│   └── index.ts             # 路由配置
├── assets/
│   ├── base.css             # 基础样式
│   └── main.css             # 主样式
├── App.vue                  # 根组件
└── main.ts                  # 应用入口
```

## API接口规范

### 1. 登录接口
```
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",     // 密码登录时必填
  "code": "123456",             // 验证码登录时必填
  "loginType": "password"       // "password" 或 "code"
}
```

### 2. 注册接口
```
POST /api/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "confirmPassword": "password123",
  "code": "123456",
  "username": "用户名"
}
```

### 3. 发送验证码
```
GET /api/auth/send-login-code?email=<EMAIL>
GET /api/auth/send-register-code?email=<EMAIL>
```

### 4. 其他接口
```
POST /api/auth/logout           # 退出登录
GET /api/auth/current-user      # 获取当前用户信息
```

## 使用方法

### 1. 启动开发服务器
```bash
npm install
npm run dev
```

### 2. 访问应用
打开浏览器访问 `http://localhost:5174`

### 3. 测试功能
- 访问首页会自动跳转到登录页面（未登录状态）
- 在登录页面可以切换到注册模式
- 支持密码登录和验证码登录
- 注册时会显示密码强度指示器
- 登录成功后跳转到首页并显示用户信息

## 组件说明

### LoginForm.vue
- 支持密码登录和验证码登录切换
- 邮箱格式验证
- 验证码发送和60秒倒计时
- 表单提交和加载状态

### RegisterForm.vue
- 用户名、邮箱、密码输入
- 实时密码强度检测
- 确认密码验证
- 注册验证码发送

### LoginView.vue
- 响应式布局设计
- 左侧品牌展示区域
- 右侧表单切换区域
- 流畅的切换动画

## 状态管理

使用 Pinia 管理认证状态：
- `user`: 当前用户信息
- `token`: 认证令牌
- `loading`: 加载状态
- `isAuthenticated`: 登录状态

## 路由守卫

- `requiresAuth`: 需要登录才能访问
- `requiresGuest`: 只有未登录用户才能访问
- 自动重定向到登录页面或首页

## 自定义配置

### 修改API基础URL
在 `src/api/auth.ts` 中修改：
```typescript
const api = axios.create({
  baseURL: '/api',  // 修改为你的API地址
  timeout: 10000
})
```

### 修改主题色彩
TDesign 支持主题定制，可以通过CSS变量修改：
```css
:root {
  --td-brand-color: #0052d9;
  --td-brand-color-hover: #266fe8;
}
```

## 注意事项

1. **后端API**: 需要实现对应的后端API接口
2. **CORS配置**: 确保后端正确配置CORS
3. **Token存储**: 当前使用localStorage存储token
4. **错误处理**: 已实现基础错误处理和用户提示
5. **安全性**: 生产环境建议使用HTTPS

## 扩展功能

可以进一步扩展的功能：
- 忘记密码功能
- 第三方登录（微信、QQ等）
- 双因子认证
- 用户头像上传
- 个人信息编辑
- 登录历史记录

## 故障排除

### 常见问题
1. **依赖安装失败**: 尝试删除 `node_modules` 和 `package-lock.json` 后重新安装
2. **端口占用**: 修改 `vite.config.ts` 中的端口配置
3. **API调用失败**: 检查后端服务是否启动和API地址是否正确

### 开发工具
- Vue DevTools: 用于调试Vue组件和状态
- Network面板: 检查API请求和响应
- Console面板: 查看错误信息和日志

---

**开发完成时间**: 2024年12月
**版本**: 1.0.0
**作者**: Augment Agent
