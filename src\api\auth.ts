import axios from 'axios'

// 创建axios实例
const api = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token')
      window.location.href = '/login'
    }
    return Promise.reject(error.response?.data || error.message)
  }
)

// 登录接口类型定义
export interface LoginRequest {
  email: string
  password?: string
  code?: string
  loginType: 'password' | 'code'
}

export interface RegisterRequest {
  email: string
  password: string
  confirmPassword: string
  code: string
  username: string
}

export interface AuthResponse {
  token: string
  user: {
    id: string
    email: string
    username: string
  }
}

// 认证API
export const authAPI = {
  // 登录
  login: (data: LoginRequest): Promise<AuthResponse> => {
    return api.post('/auth/login', data)
  },

  // 注册
  register: (data: RegisterRequest): Promise<AuthResponse> => {
    return api.post('/auth/register', data)
  },

  // 发送登录验证码
  sendLoginCode: (email: string): Promise<{ message: string }> => {
    return api.get('/auth/send-login-code', { params: { email } })
  },

  // 发送注册验证码
  sendRegisterCode: (email: string): Promise<{ message: string }> => {
    return api.get('/auth/send-register-code', { params: { email } })
  },

  // 退出登录
  logout: (): Promise<{ message: string }> => {
    return api.post('/auth/logout')
  },

  // 获取当前用户信息
  getCurrentUser: (): Promise<{ user: any }> => {
    return api.get('/auth/current-user')
  }
}
