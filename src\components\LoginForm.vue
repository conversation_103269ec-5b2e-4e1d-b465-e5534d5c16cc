<template>
  <div class="login-form">
    <t-form
      ref="formRef"
      :data="formData"
      :rules="formRules"
      :label-width="0"
      @submit="handleSubmit"
    >
      <div class="form-header">
        <h2 class="form-title">欢迎登录</h2>
        <p class="form-subtitle">请输入您的账号信息</p>
      </div>

      <t-form-item name="email">
        <t-input
          v-model="formData.email"
          placeholder="请输入邮箱地址"
          size="large"
          :prefix-icon="MailIcon"
          clearable
        />
      </t-form-item>

      <t-form-item name="loginType">
        <t-radio-group v-model="formData.loginType" variant="default-filled">
          <t-radio value="password">密码登录</t-radio>
          <t-radio value="code">验证码登录</t-radio>
        </t-radio-group>
      </t-form-item>

      <t-form-item v-if="formData.loginType === 'password'" name="password">
        <t-input
          v-model="formData.password"
          type="password"
          placeholder="请输入密码"
          size="large"
          :prefix-icon="LockOnIcon"
          clearable
        />
      </t-form-item>

      <t-form-item v-if="formData.loginType === 'code'" name="code">
        <div class="code-input-wrapper">
          <t-input
            v-model="formData.code"
            placeholder="请输入验证码"
            size="large"
            :prefix-icon="LockIcon"
            clearable
          />
          <t-button
            :disabled="!isEmailValid || countdown > 0"
            :loading="sendingCode"
            @click="handleSendCode"
            class="code-button"
          >
            {{ countdown > 0 ? `${countdown}s` : '发送验证码' }}
          </t-button>
        </div>
      </t-form-item>

      <t-form-item>
        <t-button
          type="submit"
          theme="primary"
          size="large"
          :loading="loading"
          block
          class="submit-button"
        >
          登录
        </t-button>
      </t-form-item>

      <div class="form-footer">
        <t-link theme="primary" @click="$emit('switch-mode', 'register')">
          还没有账号？立即注册
        </t-link>
      </div>
    </t-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onUnmounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { MailIcon, LockOnIcon } from 'tdesign-icons-vue-next'
import type { FormInstanceFunctions, FormRule } from 'tdesign-vue-next'

// 定义事件
const emit = defineEmits<{
  'switch-mode': [mode: 'register']
  'login-success': []
}>()

// 状态管理
const authStore = useAuthStore()
const loading = computed(() => authStore.loading)

// 表单引用
const formRef = ref<FormInstanceFunctions>()

// 表单数据
const formData = reactive({
  email: '',
  password: '',
  code: '',
  loginType: 'password' as 'password' | 'code'
})

// 验证码相关状态
const sendingCode = ref(false)
const countdown = ref(0)
let countdownTimer: number | null = null

// 邮箱格式验证
const isEmailValid = computed(() => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(formData.email)
})

// 表单验证规则
const formRules: Record<string, FormRule[]> = {
  email: [
    { required: true, message: '请输入邮箱地址', type: 'error' },
    {
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      message: '请输入正确的邮箱格式',
      type: 'error'
    }
  ],
  password: [
    { required: true, message: '请输入密码', type: 'error' },
    { min: 6, message: '密码长度不能少于6位', type: 'error' }
  ],
  code: [
    { required: true, message: '请输入验证码', type: 'error' },
    { len: 6, message: '验证码长度为6位', type: 'error' }
  ]
}

// 发送验证码
const handleSendCode = async () => {
  if (!isEmailValid.value) return

  try {
    sendingCode.value = true
    await authStore.sendLoginCode(formData.email)
    startCountdown()
  } catch (error) {
    console.error('发送验证码失败:', error)
  } finally {
    sendingCode.value = false
  }
}

// 开始倒计时
const startCountdown = () => {
  countdown.value = 60
  countdownTimer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(countdownTimer!)
      countdownTimer = null
    }
  }, 1000)
}

// 提交表单
const handleSubmit = async ({ validateResult }: any) => {
  if (validateResult === true) {
    try {
      const loginData = {
        email: formData.email,
        loginType: formData.loginType,
        ...(formData.loginType === 'password' 
          ? { password: formData.password }
          : { code: formData.code }
        )
      }
      
      await authStore.login(loginData)
      emit('login-success')
    } catch (error) {
      console.error('登录失败:', error)
    }
  }
}

// 清理定时器
onUnmounted(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
  }
})
</script>

<style scoped>
.login-form {
  width: 100%;
  max-width: 400px;
}

.form-header {
  text-align: center;
  margin-bottom: 32px;
}

.form-title {
  font-size: 28px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin: 0 0 8px 0;
}

.form-subtitle {
  font-size: 14px;
  color: var(--td-text-color-secondary);
  margin: 0;
}

.code-input-wrapper {
  display: flex;
  gap: 12px;
}

.code-input-wrapper .t-input {
  flex: 1;
}

.code-button {
  white-space: nowrap;
  min-width: 100px;
}

.submit-button {
  margin-top: 16px;
  height: 44px;
  font-size: 16px;
  font-weight: 500;
}

.form-footer {
  text-align: center;
  margin-top: 24px;
}

:deep(.t-form-item) {
  margin-bottom: 20px;
}

:deep(.t-radio-group) {
  width: 100%;
  justify-content: center;
}

:deep(.t-radio) {
  margin-right: 24px;
}
</style>
