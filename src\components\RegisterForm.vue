<template>
  <div class="register-form">
    <t-form
      ref="formRef"
      :data="formData"
      :rules="formRules"
      :label-width="0"
      @submit="handleSubmit"
    >
      <div class="form-header">
        <h2 class="form-title">创建账号</h2>
        <p class="form-subtitle">请填写以下信息完成注册</p>
      </div>

      <t-form-item name="username">
        <t-input
          v-model="formData.username"
          placeholder="请输入用户名"
          size="large"
          :prefix-icon="UserIcon"
          clearable
        />
      </t-form-item>

      <t-form-item name="email">
        <t-input
          v-model="formData.email"
          placeholder="请输入邮箱地址"
          size="large"
          :prefix-icon="MailIcon"
          clearable
        />
      </t-form-item>

      <t-form-item name="password">
        <t-input
          v-model="formData.password"
          type="password"
          placeholder="请输入密码"
          size="large"
          :prefix-icon="LockOnIcon"
          clearable
        />
        <div class="password-strength">
          <div class="strength-bar">
            <div 
              class="strength-fill" 
              :class="passwordStrengthClass"
              :style="{ width: passwordStrengthWidth }"
            ></div>
          </div>
          <span class="strength-text" :class="passwordStrengthClass">
            {{ passwordStrengthText }}
          </span>
        </div>
      </t-form-item>

      <t-form-item name="confirmPassword">
        <t-input
          v-model="formData.confirmPassword"
          type="password"
          placeholder="请确认密码"
          size="large"
          :prefix-icon="LockOnIcon"
          clearable
        />
      </t-form-item>

      <t-form-item name="code">
        <div class="code-input-wrapper">
          <t-input
            v-model="formData.code"
            placeholder="请输入验证码"
            size="large"
            :prefix-icon="SecurityIcon"
            clearable
          />
          <t-button
            :disabled="!isEmailValid || countdown > 0"
            :loading="sendingCode"
            @click="handleSendCode"
            class="code-button"
          >
            {{ countdown > 0 ? `${countdown}s` : '发送验证码' }}
          </t-button>
        </div>
      </t-form-item>

      <t-form-item>
        <t-button
          type="submit"
          theme="primary"
          size="large"
          :loading="loading"
          block
          class="submit-button"
        >
          注册
        </t-button>
      </t-form-item>

      <div class="form-footer">
        <t-link theme="primary" @click="$emit('switch-mode', 'login')">
          已有账号？立即登录
        </t-link>
      </div>
    </t-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onUnmounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { UserIcon, MailIcon, LockOnIcon, SecurityIcon } from 'tdesign-icons-vue-next'
import type { FormInstanceFunctions, FormRule } from 'tdesign-vue-next'

// 定义事件
const emit = defineEmits<{
  'switch-mode': [mode: 'login']
  'register-success': []
}>()

// 状态管理
const authStore = useAuthStore()
const loading = computed(() => authStore.loading)

// 表单引用
const formRef = ref<FormInstanceFunctions>()

// 表单数据
const formData = reactive({
  username: '',
  email: '',
  password: '',
  confirmPassword: '',
  code: ''
})

// 验证码相关状态
const sendingCode = ref(false)
const countdown = ref(0)
let countdownTimer: number | null = null

// 邮箱格式验证
const isEmailValid = computed(() => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(formData.email)
})

// 密码强度计算
const passwordStrength = computed(() => {
  const password = formData.password
  if (!password) return 0
  
  let strength = 0
  if (password.length >= 8) strength += 1
  if (/[a-z]/.test(password)) strength += 1
  if (/[A-Z]/.test(password)) strength += 1
  if (/[0-9]/.test(password)) strength += 1
  if (/[^A-Za-z0-9]/.test(password)) strength += 1
  
  return strength
})

const passwordStrengthText = computed(() => {
  const strength = passwordStrength.value
  if (strength === 0) return ''
  if (strength <= 2) return '弱'
  if (strength <= 3) return '中'
  return '强'
})

const passwordStrengthClass = computed(() => {
  const strength = passwordStrength.value
  if (strength <= 2) return 'weak'
  if (strength <= 3) return 'medium'
  return 'strong'
})

const passwordStrengthWidth = computed(() => {
  return `${(passwordStrength.value / 5) * 100}%`
})

// 表单验证规则
const formRules: Record<string, FormRule[]> = {
  username: [
    { required: true, message: '请输入用户名', type: 'error' },
    { min: 2, message: '用户名长度不能少于2位', type: 'error' },
    { max: 20, message: '用户名长度不能超过20位', type: 'error' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', type: 'error' },
    {
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      message: '请输入正确的邮箱格式',
      type: 'error'
    }
  ],
  password: [
    { required: true, message: '请输入密码', type: 'error' },
    { min: 6, message: '密码长度不能少于6位', type: 'error' },
    {
      validator: (val: string) => passwordStrength.value >= 2,
      message: '密码强度太弱，请包含字母、数字或特殊字符',
      type: 'error'
    }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', type: 'error' },
    {
      validator: (val: string) => val === formData.password,
      message: '两次输入的密码不一致',
      type: 'error'
    }
  ],
  code: [
    { required: true, message: '请输入验证码', type: 'error' },
    { len: 6, message: '验证码长度为6位', type: 'error' }
  ]
}

// 发送验证码
const handleSendCode = async () => {
  if (!isEmailValid.value) return

  try {
    sendingCode.value = true
    await authStore.sendRegisterCode(formData.email)
    startCountdown()
  } catch (error) {
    console.error('发送验证码失败:', error)
  } finally {
    sendingCode.value = false
  }
}

// 开始倒计时
const startCountdown = () => {
  countdown.value = 60
  countdownTimer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(countdownTimer!)
      countdownTimer = null
    }
  }, 1000)
}

// 提交表单
const handleSubmit = async ({ validateResult }: any) => {
  if (validateResult === true) {
    try {
      await authStore.register(formData)
      emit('register-success')
    } catch (error) {
      console.error('注册失败:', error)
    }
  }
}

// 清理定时器
onUnmounted(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
  }
})
</script>

<style scoped>
.register-form {
  width: 100%;
  max-width: 400px;
}

.form-header {
  text-align: center;
  margin-bottom: 32px;
}

.form-title {
  font-size: 28px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin: 0 0 8px 0;
}

.form-subtitle {
  font-size: 14px;
  color: var(--td-text-color-secondary);
  margin: 0;
}

.code-input-wrapper {
  display: flex;
  gap: 12px;
}

.code-input-wrapper .t-input {
  flex: 1;
}

.code-button {
  white-space: nowrap;
  min-width: 100px;
}

.password-strength {
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.strength-bar {
  flex: 1;
  height: 4px;
  background-color: var(--td-bg-color-component);
  border-radius: 2px;
  overflow: hidden;
}

.strength-fill {
  height: 100%;
  border-radius: 2px;
  transition: all 0.3s ease;
}

.strength-fill.weak {
  background-color: var(--td-error-color);
}

.strength-fill.medium {
  background-color: var(--td-warning-color);
}

.strength-fill.strong {
  background-color: var(--td-success-color);
}

.strength-text {
  font-size: 12px;
  font-weight: 500;
  min-width: 20px;
}

.strength-text.weak {
  color: var(--td-error-color);
}

.strength-text.medium {
  color: var(--td-warning-color);
}

.strength-text.strong {
  color: var(--td-success-color);
}

.submit-button {
  margin-top: 16px;
  height: 44px;
  font-size: 16px;
  font-weight: 500;
}

.form-footer {
  text-align: center;
  margin-top: 24px;
}

:deep(.t-form-item) {
  margin-bottom: 20px;
}
</style>
