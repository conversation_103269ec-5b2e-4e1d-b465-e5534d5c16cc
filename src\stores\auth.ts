import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authAPI, type LoginRequest, type RegisterRequest } from '@/api/auth'
import { MessagePlugin } from 'tdesign-vue-next'

export interface User {
  id: string
  email: string
  username: string
}

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('token'))
  const loading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)

  // 设置认证信息
  const setAuth = (authData: { token: string; user: User }) => {
    token.value = authData.token
    user.value = authData.user
    localStorage.setItem('token', authData.token)
  }

  // 清除认证信息
  const clearAuth = () => {
    token.value = null
    user.value = null
    localStorage.removeItem('token')
  }

  // 登录
  const login = async (loginData: LoginRequest) => {
    try {
      loading.value = true
      const response = await authAPI.login(loginData)
      setAuth(response)
      MessagePlugin.success('登录成功')
      return response
    } catch (error: any) {
      MessagePlugin.error(error.message || '登录失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 注册
  const register = async (registerData: RegisterRequest) => {
    try {
      loading.value = true
      const response = await authAPI.register(registerData)
      setAuth(response)
      MessagePlugin.success('注册成功')
      return response
    } catch (error: any) {
      MessagePlugin.error(error.message || '注册失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 退出登录
  const logout = async () => {
    try {
      await authAPI.logout()
      clearAuth()
      MessagePlugin.success('退出登录成功')
    } catch (error: any) {
      // 即使API调用失败，也要清除本地认证信息
      clearAuth()
      MessagePlugin.error(error.message || '退出登录失败')
    }
  }

  // 获取当前用户信息
  const getCurrentUser = async () => {
    try {
      if (!token.value) return null
      const response = await authAPI.getCurrentUser()
      user.value = response.user
      return response.user
    } catch (error: any) {
      clearAuth()
      throw error
    }
  }

  // 发送登录验证码
  const sendLoginCode = async (email: string) => {
    try {
      const response = await authAPI.sendLoginCode(email)
      MessagePlugin.success('验证码发送成功')
      return response
    } catch (error: any) {
      MessagePlugin.error(error.message || '验证码发送失败')
      throw error
    }
  }

  // 发送注册验证码
  const sendRegisterCode = async (email: string) => {
    try {
      const response = await authAPI.sendRegisterCode(email)
      MessagePlugin.success('验证码发送成功')
      return response
    } catch (error: any) {
      MessagePlugin.error(error.message || '验证码发送失败')
      throw error
    }
  }

  // 初始化认证状态
  const initAuth = async () => {
    if (token.value) {
      try {
        await getCurrentUser()
      } catch (error) {
        clearAuth()
      }
    }
  }

  return {
    // 状态
    user,
    token,
    loading,
    // 计算属性
    isAuthenticated,
    // 方法
    login,
    register,
    logout,
    getCurrentUser,
    sendLoginCode,
    sendRegisterCode,
    initAuth,
    setAuth,
    clearAuth
  }
})
