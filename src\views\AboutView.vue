<template>
  <div class="about-page">
    <t-layout>
      <t-header class="header">
        <div class="header-content">
          <div class="logo">
            <span class="logo-icon">📚</span>
            <span class="logo-text">BookSystem</span>
          </div>
          <t-button @click="$router.push('/')" variant="text">
            返回首页
          </t-button>
        </div>
      </t-header>
      
      <t-content class="content">
        <div class="about-section">
          <h1>关于 BookSystem</h1>
          <p>这是一个现代化的图书管理系统，使用 Vue 3 + TDesign 构建。</p>
          
          <t-card title="技术栈" class="tech-card">
            <div class="tech-list">
              <div class="tech-item">Vue 3</div>
              <div class="tech-item">TypeScript</div>
              <div class="tech-item">TDesign Vue Next</div>
              <div class="tech-item">Pinia</div>
              <div class="tech-item">Vue Router</div>
              <div class="tech-item">Axios</div>
            </div>
          </t-card>
        </div>
      </t-content>
    </t-layout>
  </div>
</template>

<style scoped>
.about-page {
  min-height: 100vh;
  background-color: var(--td-bg-color-page);
}

.header {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  height: 64px;
  max-width: 1200px;
  margin: 0 auto;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 20px;
  font-weight: 600;
  color: var(--td-text-color-primary);
}

.logo-icon {
  font-size: 24px;
}

.content {
  padding: 40px 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.about-section {
  text-align: center;
}

.about-section h1 {
  font-size: 32px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin-bottom: 16px;
}

.about-section p {
  font-size: 16px;
  color: var(--td-text-color-secondary);
  margin-bottom: 40px;
}

.tech-card {
  max-width: 600px;
  margin: 0 auto;
}

.tech-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.tech-item {
  padding: 12px 16px;
  background: var(--td-bg-color-container);
  border-radius: 8px;
  font-weight: 500;
  color: var(--td-text-color-primary);
}
</style>
