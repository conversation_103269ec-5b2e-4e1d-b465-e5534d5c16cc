<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const user = computed(() => authStore.user)

const handleLogout = async () => {
  await authStore.logout()
  router.push('/login')
}
</script>

<template>
  <div class="home-page">
    <t-layout>
      <t-header class="header">
        <div class="header-content">
          <div class="logo">
            <span class="logo-icon">📚</span>
            <span class="logo-text">BookSystem</span>
          </div>
          <div class="user-info">
            <t-dropdown :options="[
              { content: '个人设置', value: 'profile' },
              { content: '退出登录', value: 'logout' }
            ]" @click="(data) => data.value === 'logout' && handleLogout()">
              <t-button variant="text" class="user-button">
                <t-icon name="user-circle" />
                {{ user?.username || user?.email }}
                <t-icon name="chevron-down" />
              </t-button>
            </t-dropdown>
          </div>
        </div>
      </t-header>
      
      <t-content class="content">
        <div class="welcome-section">
          <h1>欢迎使用 BookSystem</h1>
          <p>您已成功登录系统</p>
          <div class="user-card">
            <t-card title="用户信息" :bordered="false">
              <div class="user-details">
                <div class="detail-item">
                  <span class="label">用户名：</span>
                  <span class="value">{{ user?.username }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">邮箱：</span>
                  <span class="value">{{ user?.email }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">用户ID：</span>
                  <span class="value">{{ user?.id }}</span>
                </div>
              </div>
            </t-card>
          </div>
        </div>
      </t-content>
    </t-layout>
  </div>
</template>

<style scoped>
.home-page {
  min-height: 100vh;
  background-color: var(--td-bg-color-page);
}

.header {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  height: 64px;
  max-width: 1200px;
  margin: 0 auto;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 20px;
  font-weight: 600;
  color: var(--td-text-color-primary);
}

.logo-icon {
  font-size: 24px;
}

.user-button {
  display: flex;
  align-items: center;
  gap: 8px;
}

.content {
  padding: 40px 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-section {
  text-align: center;
}

.welcome-section h1 {
  font-size: 32px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin-bottom: 16px;
}

.welcome-section p {
  font-size: 16px;
  color: var(--td-text-color-secondary);
  margin-bottom: 40px;
}

.user-card {
  max-width: 400px;
  margin: 0 auto;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.label {
  font-weight: 500;
  color: var(--td-text-color-secondary);
}

.value {
  color: var(--td-text-color-primary);
}
</style>
