<template>
  <div class="login-page">
    <div class="login-container">
      <!-- 背景装饰 -->
      <div class="background-decoration">
        <div class="decoration-circle circle-1"></div>
        <div class="decoration-circle circle-2"></div>
        <div class="decoration-circle circle-3"></div>
      </div>

      <!-- 左侧品牌区域 -->
      <div class="brand-section">
        <div class="brand-content">
          <div class="brand-logo">
            <div class="logo-icon">📚</div>
            <h1 class="brand-title">BookSystem</h1>
          </div>
          <p class="brand-description">
            现代化的图书管理系统，为您提供便捷的图书管理体验
          </p>
          <div class="feature-list">
            <div class="feature-item">
              <t-icon name="check-circle" />
              <span>智能图书管理</span>
            </div>
            <div class="feature-item">
              <t-icon name="check-circle" />
              <span>多端数据同步</span>
            </div>
            <div class="feature-item">
              <t-icon name="check-circle" />
              <span>安全可靠</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧表单区域 -->
      <div class="form-section">
        <div class="form-container">
          <div class="form-tabs">
            <div 
              class="tab-item"
              :class="{ active: currentMode === 'login' }"
              @click="switchMode('login')"
            >
              登录
            </div>
            <div 
              class="tab-item"
              :class="{ active: currentMode === 'register' }"
              @click="switchMode('register')"
            >
              注册
            </div>
            <div class="tab-indicator" :class="currentMode"></div>
          </div>

          <div class="form-content">
            <transition name="slide" mode="out-in">
              <LoginForm
                v-if="currentMode === 'login'"
                key="login"
                @switch-mode="switchMode"
                @login-success="handleAuthSuccess"
              />
              <RegisterForm
                v-else
                key="register"
                @switch-mode="switchMode"
                @register-success="handleAuthSuccess"
              />
            </transition>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import LoginForm from '@/components/LoginForm.vue'
import RegisterForm from '@/components/RegisterForm.vue'

const router = useRouter()
const currentMode = ref<'login' | 'register'>('login')

// 切换模式
const switchMode = (mode: 'login' | 'register') => {
  currentMode.value = mode
}

// 认证成功处理
const handleAuthSuccess = () => {
  router.push('/')
}
</script>

<style scoped>
.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  overflow: hidden;
}

.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.circle-3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

.login-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  display: flex;
  width: 100%;
  max-width: 1000px;
  min-height: 600px;
  overflow: hidden;
  position: relative;
}

.brand-section {
  flex: 1;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  padding: 60px 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.brand-content {
  text-align: center;
  max-width: 400px;
}

.brand-logo {
  margin-bottom: 32px;
}

.logo-icon {
  font-size: 64px;
  margin-bottom: 16px;
}

.brand-title {
  font-size: 36px;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(45deg, #fff, #e0e7ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.brand-description {
  font-size: 16px;
  line-height: 1.6;
  margin: 0 0 40px 0;
  opacity: 0.9;
}

.feature-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
}

.form-section {
  flex: 1;
  padding: 60px 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.form-container {
  width: 100%;
  max-width: 400px;
}

.form-tabs {
  display: flex;
  margin-bottom: 40px;
  position: relative;
  background: var(--td-bg-color-container);
  border-radius: 12px;
  padding: 4px;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.tab-item.active {
  color: var(--td-brand-color);
}

.tab-indicator {
  position: absolute;
  top: 4px;
  bottom: 4px;
  width: calc(50% - 4px);
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  z-index: 1;
}

.tab-indicator.login {
  transform: translateX(4px);
}

.tab-indicator.register {
  transform: translateX(calc(100% + 4px));
}

.form-content {
  position: relative;
}

/* 过渡动画 */
.slide-enter-active,
.slide-leave-active {
  transition: all 0.3s ease;
}

.slide-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.slide-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-container {
    flex-direction: column;
    max-width: 400px;
    min-height: auto;
  }

  .brand-section {
    padding: 40px 20px;
  }

  .brand-title {
    font-size: 28px;
  }

  .form-section {
    padding: 40px 20px;
  }

  .circle-1,
  .circle-2,
  .circle-3 {
    display: none;
  }
}

@media (max-width: 480px) {
  .login-page {
    padding: 10px;
  }

  .brand-section {
    padding: 30px 15px;
  }

  .form-section {
    padding: 30px 15px;
  }
}
</style>
